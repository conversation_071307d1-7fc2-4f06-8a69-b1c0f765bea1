import React, { useState } from 'react';
import { getImageUrl, getSmartFileUrl, getPlaceholderImage } from '../../utils/constants';

const ThumbnailDebug = () => {
  const [testUrl, setTestUrl] = useState('');
  
  // Test URLs
  const testUrls = [
    '/uploads/thumbnails/test.jpg',
    'uploads/thumbnails/test.jpg',
    'test.jpg',
    'https://example.com/test.jpg',
    'http://localhost:5000/uploads/thumbnails/test.jpg',
    ''
  ];

  return (
    <div style={{ padding: '20px', border: '1px solid #ccc', margin: '20px' }}>
      <h3>Thumbnail URL Debug Tool</h3>
      
      <div style={{ marginBottom: '20px' }}>
        <label>Test URL: </label>
        <input 
          type="text" 
          value={testUrl} 
          onChange={(e) => setTestUrl(e.target.value)}
          style={{ width: '300px', padding: '5px' }}
          placeholder="Enter thumbnail URL to test"
        />
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h4>Test Results for: "{testUrl}"</h4>
        <p><strong>getImageUrl:</strong> {getImageUrl(testUrl)}</p>
        <p><strong>getSmartFileUrl:</strong> {getSmartFileUrl(testUrl)}</p>
        
        {testUrl && (
          <div style={{ marginTop: '10px' }}>
            <p><strong>Image Preview (getImageUrl):</strong></p>
            <img 
              src={getImageUrl(testUrl)} 
              alt="Test thumbnail"
              style={{ width: '100px', height: '60px', objectFit: 'cover', border: '1px solid #ddd' }}
              onError={(e) => {
                console.error('Image failed to load:', getImageUrl(testUrl));
                e.target.src = getPlaceholderImage(100, 60, 'Failed');
              }}
              onLoad={() => {
                console.log('Image loaded successfully:', getImageUrl(testUrl));
              }}
            />
          </div>
        )}
      </div>

      <div>
        <h4>Predefined Test URLs:</h4>
        {testUrls.map((url, index) => (
          <div key={index} style={{ marginBottom: '10px', padding: '10px', backgroundColor: '#f5f5f5' }}>
            <p><strong>Input:</strong> "{url}"</p>
            <p><strong>getImageUrl:</strong> {getImageUrl(url)}</p>
            <p><strong>getSmartFileUrl:</strong> {getSmartFileUrl(url)}</p>
            <button onClick={() => setTestUrl(url)}>Test This URL</button>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ThumbnailDebug;
