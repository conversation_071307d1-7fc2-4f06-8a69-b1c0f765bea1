import React from "react";
import { FaTimes, FaQuestionCircle, FaUser, FaClock, FaTag, FaToggleOn, FaToggleOff } from "react-icons/fa";
import "../../styles/FAQDetailModal.css";

const FAQDetailModal = ({ faq, isOpen, onClose }) => {
  if (!isOpen || !faq) return null;

  const formatDate = (date) => {
    return new Date(date).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const getStatusBadge = (status) => {
    return status === "active" ? "status-badge active" : "status-badge inactive";
  };

  const handleOverlayClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div className="modal-overlay" onClick={handleOverlayClick}>
      <div className="FAQDetailModal">
        <div className="modal-header">
          <div className="header-content">
            <FaQuestionCircle className="header-icon" />
            <h2 className="modal-title">FAQ Details</h2>
          </div>
          <button className="close-btn" onClick={onClose} aria-label="Close modal">
            <FaTimes />
          </button>
        </div>

        <div className="modal-body">
          {/* Status and Order */}
          <div className="detail-row">
            <div className="detail-group">
              <label className="detail-label">Status</label>
              <div className="status-display">
                <span className={getStatusBadge(faq.status)}>
                  {faq.status === "active" ? <FaToggleOn /> : <FaToggleOff />}
                  {faq.status}
                </span>
              </div>
            </div>
            <div className="detail-group">
              <label className="detail-label">Display Order</label>
              <span className="order-badge">{faq.order}</span>
            </div>
          </div>

          {/* Category */}
          {faq.category && (
            <div className="detail-row">
              <div className="detail-group full-width">
                <label className="detail-label">
                  <FaTag className="label-icon" />
                  Category
                </label>
                <span className="category-tag">{faq.category}</span>
              </div>
            </div>
          )}

          {/* Question */}
          <div className="detail-row">
            <div className="detail-group full-width">
              <label className="detail-label">Question</label>
              <div className="question-display">
                {faq.question}
              </div>
            </div>
          </div>

          {/* Answer */}
          <div className="detail-row">
            <div className="detail-group full-width">
              <label className="detail-label">Answer</label>
              <div className="answer-display">
                {faq.answer}
              </div>
            </div>
          </div>

          {/* Metadata */}
          <div className="metadata-section">
            <h3 className="metadata-title">Metadata</h3>
            
            <div className="metadata-grid">
              <div className="metadata-item">
                <label className="metadata-label">
                  <FaUser className="label-icon" />
                  Created By
                </label>
                <span className="metadata-value">
                  {faq.createdBy 
                    ? `${faq.createdBy.firstName} ${faq.createdBy.lastName}`
                    : "Unknown"
                  }
                </span>
              </div>

              <div className="metadata-item">
                <label className="metadata-label">
                  <FaClock className="label-icon" />
                  Created At
                </label>
                <span className="metadata-value">
                  {formatDate(faq.createdAt)}
                </span>
              </div>

              {faq.updatedBy && (
                <div className="metadata-item">
                  <label className="metadata-label">
                    <FaUser className="label-icon" />
                    Updated By
                  </label>
                  <span className="metadata-value">
                    {`${faq.updatedBy.firstName} ${faq.updatedBy.lastName}`}
                  </span>
                </div>
              )}

              {faq.updatedAt && (
                <div className="metadata-item">
                  <label className="metadata-label">
                    <FaClock className="label-icon" />
                    Updated At
                  </label>
                  <span className="metadata-value">
                    {formatDate(faq.updatedAt)}
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="modal-footer">
          <button className="btn-secondary" onClick={onClose}>
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default FAQDetailModal;
