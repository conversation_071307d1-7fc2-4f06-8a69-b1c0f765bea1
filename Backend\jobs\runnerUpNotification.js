const Order = require('../models/Order');
const Content = require('../models/Content');
const User = require('../models/User');
const Bid = require('../models/Bid');
const sendEmail = require('../utils/sendEmail');
const { getPaymentDeadline } = require('../config/timeouts');

class RunnerUpNotificationJob {
    async execute() {
        return await this.run();
    }

    async run() {
        try {
            console.log('🏃 Starting runner-up notification job...');

            const results = {
                expiredOrdersProcessed: 0,
                runnerUpFound: 0,
                runnerUpNotified: 0,
                runnerUpSkipped: 0,
                ordersCreated: 0,
                errors: []
            };

            // Find expired orders from auctions that might have runner-up bidders
            const expiredAuctionOrders = await Order.find({
                paymentStatus: 'Expired',
                orderType: 'Auction',
                bidId: { $exists: true },
                // Only process orders that expired recently (within last 2 hours)
                expiredAt: {
                    $gte: new Date(Date.now() - 2 * 60 * 60 * 1000),
                    $exists: true
                },
                // Add check for runnerUpNotified flag
                runnerUpNotified: { $ne: true }
            })
                .populate('bidId')
                .populate('content', 'title sport contentType saleType')
                .populate('seller', 'firstName lastName email')
                .lean();

            console.log(`Found ${expiredAuctionOrders.length} expired auction orders to process for runner-up`);

            for (const expiredOrder of expiredAuctionOrders) {
                try {
                    await this.processRunnerUpForExpiredOrder(expiredOrder, results);

                    // Mark the order as notified to prevent duplicate processing
                    await Order.findByIdAndUpdate(expiredOrder._id, {
                        runnerUpNotified: true
                    });

                } catch (error) {
                    console.error(`Error processing runner-up for order ${expiredOrder._id}:`, error);
                    results.errors.push({
                        orderId: expiredOrder._id,
                        error: error.message
                    });
                }
            }

            console.log('✅ Runner-up notification job completed:', results);
            return results;

        } catch (error) {
            console.error('❌ Runner-up notification job failed:', error);
            throw error;
        }
    }

    async processRunnerUpForExpiredOrder(expiredOrder, results) {
        results.expiredOrdersProcessed++;

        if (!expiredOrder.bidId || !expiredOrder.content) {
            console.log(`Skipping order ${expiredOrder._id} - missing bid or content info`);
            return;
        }

        // Find the next highest bidder
        const runnerUpBid = await Bid.findOne({
            content: expiredOrder.content._id,
            status: 'Active',
            _id: { $ne: expiredOrder.bidId._id }
        })
            .sort('-amount')
            .populate('bidder', 'firstName lastName email fraudDetection');

        if (!runnerUpBid) {
            console.log(`No runner-up bidder found for content: ${expiredOrder.content.title}`);
            return;
        }

        results.runnerUpFound++;

        // Check if runner-up is eligible to make purchase
        const runnerUpBuyer = await User.findById(runnerUpBid.bidder._id);
        const purchaseCheck = runnerUpBuyer.canMakePurchase();

        if (!purchaseCheck.allowed) {
            console.log(`Runner-up bidder ${runnerUpBuyer.email} is not eligible: ${purchaseCheck.reason}`);
            results.runnerUpSkipped++;

            // Try to find the next runner-up
            const nextRunnerUp = await this.findNextEligibleBidder(
                expiredOrder.content._id,
                [expiredOrder.bidId._id, runnerUpBid._id]
            );

            if (nextRunnerUp) {
                return await this.createRunnerUpOrder(nextRunnerUp, expiredOrder, results);
            }
            return;
        }

        // Process the runner-up bid
        await this.createRunnerUpOrder(runnerUpBid, expiredOrder, results);
    }

    async findNextEligibleBidder(contentId, excludeBidIds) {
        const nextBids = await Bid.find({
            content: contentId,
            status: 'Active',
            _id: { $nin: excludeBidIds }
        })
            .sort('-amount')
            .populate('bidder', 'firstName lastName email fraudDetection')
            .limit(3); // Check up to 3 more bidders

        for (const bid of nextBids) {
            const buyer = await User.findById(bid.bidder._id);
            const purchaseCheck = buyer.canMakePurchase();

            if (purchaseCheck.allowed) {
                console.log(`Found eligible next bidder: ${buyer.email} with bid $${bid.amount}`);
                return bid;
            }
        }

        return null;
    }

    async createRunnerUpOrder(runnerUpBid, originalExpiredOrder, results) {
        try {
            // Calculate new order details using database settings
            const Setting = require("../models/Setting");
            const settings = await Setting.getSingleton();
            const amount = runnerUpBid.amount;
            const platformFeePercentage = settings.financial?.platformCommissionPercentage || 5;
            const platformFee = Math.round(amount * (platformFeePercentage / 100) * 100) / 100;
            const sellerEarnings = amount - platformFee;

            // Update content to reflect pending runner-up bid
            await Content.findByIdAndUpdate(originalExpiredOrder.content._id, {
                auctionStatus: 'Active', // Keep auction active for seller to accept runner-up
                isSold: false,
                soldAt: null,
                winningBidId: null // Clear previous winning bid
            });

            // Send notifications about runner-up bid availability
            await this.sendRunnerUpNotifications(runnerUpBid, originalExpiredOrder, results);

            results.runnerUpNotified++;

            console.log(`Notified seller about available runner-up bid from ${runnerUpBid.bidder.email}: $${amount}`);

        } catch (error) {
            console.error(`Error processing runner-up bid:`, error);
            throw error;
        }
    }

    async sendRunnerUpNotifications(runnerUpBid, originalExpiredOrder, results) {
        try {
            // Notify seller about available runner-up bid
            await this.sendRunnerUpSellerNotification(runnerUpBid, originalExpiredOrder);

            // Notify runner-up bidder that their bid is available for acceptance
            await this.sendRunnerUpBuyerNotification(runnerUpBid);

        } catch (error) {
            console.error('Error sending runner-up notifications:', error);
            // Don't throw - just log the error
        }
    }

    async sendRunnerUpBuyerNotification(runnerUpBid) {
        if (!runnerUpBid.bidder || !runnerUpBid.bidder.email) return;

        const emailData = {
            to: runnerUpBid.bidder.email,
            subject: '🎉 Your bid is now the highest!',
            html: `
                <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                    <h2>Your bid is now the highest!</h2>
                    <p>Hello ${runnerUpBid.bidder.firstName},</p>
                    <p>The previous winning bidder's payment has expired, and your bid of $${runnerUpBid.amount} is now the highest bid.</p>
                    <p>The seller will review your bid and may accept it. We'll notify you if your bid is accepted.</p>
                    <p>Thank you for participating!</p>
                    <p>Best regards,<br>The XO Sports Hub Team</p>
                </div>
            `
        };

        await sendEmail(emailData);
    }

    async sendRunnerUpSellerNotification(runnerUpBid, originalExpiredOrder) {
        if (!originalExpiredOrder.seller || !originalExpiredOrder.seller.email) return;

        const emailData = {
            to: originalExpiredOrder.seller.email,
            subject: '🔔 Runner-up bid available',
            html: `
                <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                    <h2>Runner-up bid available</h2>
                    <p>Hello ${originalExpiredOrder.seller.firstName},</p>
                    <p>The winning bidder's payment has expired, but there is a runner-up bid available:</p>
                    <ul>
                        <li>Bidder: ${runnerUpBid.bidder.firstName} ${runnerUpBid.bidder.lastName}</li>
                        <li>Bid Amount: $${runnerUpBid.amount}</li>
                        <li>Content: ${originalExpiredOrder.content.title}</li>
                    </ul>
                    <p>Please review and accept this bid if you'd like to proceed with the sale.</p>
                    <p>Best regards,<br>The XO Sports Hub Team</p>
                </div>
            `
        };

        await sendEmail(emailData);
    }
}

module.exports = new RunnerUpNotificationJob(); 