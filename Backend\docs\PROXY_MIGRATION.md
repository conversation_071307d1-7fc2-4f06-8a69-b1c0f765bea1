# Proxy System Migration Guide

This document describes the migration from AWS S3 signed URLs to the secure backend proxy system in XOSportsHub.

## Overview

The new proxy system replaces the complex signed URL management with a simpler, more secure approach:

- **Before**: Generate signed URLs with 7-day expiration, refresh every 6 days
- **After**: Direct proxy endpoints that handle authentication and stream content securely

## Key Benefits

1. **No URL Expiration**: Proxy URLs never expire
2. **Better Security**: S3 URLs are never exposed to frontend
3. **Simplified Architecture**: No background refresh services needed
4. **Better Performance**: Direct streaming with range support
5. **Easier Maintenance**: No complex URL management logic

## New Proxy Endpoints

### Backend Endpoints

- `GET /api/proxy/content/:contentId` - Serve full content files
- `GET /api/proxy/preview/:contentId` - Serve preview files
- `GET /api/proxy/stream/:contentId` - Stream video content with range support
- `GET /api/proxy/thumbnail/:contentId` - Serve thumbnail images

### Authentication

All proxy endpoints require authentication via JWT token in Authorization header:
```
Authorization: Bearer <jwt_token>
```

### Access Control

The proxy system integrates with existing access control:
- **Full Access**: Users who purchased the content, content owners, admins
- **Preview Access**: All authenticated users (for preview content)
- **No Access**: Unauthenticated users or users without purchase

## Frontend Migration

### Old Approach (Deprecated)
```javascript
// OLD - Using signed URLs
import { getSignedFileUrl, getVideoStreamUrl } from '../utils/constants';

const fileUrl = await getSignedFileUrl(content.fileUrl);
const streamUrl = getVideoStreamUrl(content.fileUrl);
```

### New Approach (Recommended)
```javascript
// NEW - Using proxy URLs
import { getProxyContentUrl, getProxyStreamUrl } from '../utils/constants';

const fileUrl = getProxyContentUrl(content._id);
const streamUrl = getProxyStreamUrl(content._id);
```

### Component Update Example

**Before:**
```jsx
// OLD DocumentViewer usage
<DocumentViewer
  fileUrl={await getSignedFileUrl(content.fileUrl)}
  fileName={fileName}
  title={title}
/>
```

**After:**
```jsx
// NEW DocumentViewer usage
<DocumentViewer
  fileUrl={getProxyPreviewUrl(content._id)}
  fileName={fileName}
  title={title}
/>
```

### Video Component Update

**Before:**
```jsx
// OLD video usage
<video controls>
  <source src={getVideoStreamUrl(content.fileUrl)} type="video/mp4" />
</video>
```

**After:**
```jsx
// NEW video usage
<video controls>
  <source src={getProxyStreamUrl(content._id)} type="video/mp4" />
</video>
```

## Migration Steps

### Phase 1: Backend (Completed)
- ✅ Created proxy routes and controllers
- ✅ Removed signed URL generation functions
- ✅ Removed URL refresh middleware and services
- ✅ Updated route configurations

### Phase 2: Frontend (In Progress)
1. Update components to use new proxy utility functions
2. Replace content file URLs with content IDs
3. Test all media viewers and document components
4. Remove legacy signed URL functions

### Phase 3: Cleanup
1. Remove deprecated endpoints
2. Clean up unused utility functions
3. Update documentation

## Testing

### Manual Testing
1. **Authentication**: Verify proxy endpoints require valid JWT
2. **Access Control**: Test with different user roles and purchase status
3. **Media Playback**: Test video seeking and PDF viewing
4. **Error Handling**: Test with invalid content IDs and unauthorized access

### Automated Testing
```bash
# Test proxy endpoints
curl -H "Authorization: Bearer <token>" \
  http://localhost:5000/api/proxy/content/<contentId>

# Test streaming with range requests
curl -H "Authorization: Bearer <token>" \
  -H "Range: bytes=0-1023" \
  http://localhost:5000/api/proxy/stream/<contentId>
```

## Troubleshooting

### Common Issues

1. **401 Unauthorized**: Check JWT token in Authorization header
2. **403 Forbidden**: User doesn't have access to content (purchase required)
3. **404 Not Found**: Invalid content ID or content doesn't exist
4. **500 Server Error**: Check S3 configuration and file existence

### Debug Logging

Enable debug logging to troubleshoot issues:
```javascript
// In proxy controller
console.log(`[Proxy] User ${userId} accessing content ${contentId}`);
console.log(`[Proxy] Access level:`, fileAccess);
```

## Performance Considerations

1. **Caching**: Thumbnails are cached for 1 hour
2. **Range Requests**: Video streaming supports byte-range requests for seeking
3. **Memory Usage**: Files are streamed directly from S3, not loaded into memory
4. **Concurrent Access**: No limits on concurrent proxy requests

## Security Features

1. **No URL Exposure**: S3 URLs never sent to frontend
2. **Access Validation**: Every request validates user permissions
3. **Audit Trail**: All access attempts are logged
4. **Rate Limiting**: Can be added per user/IP if needed

## Rollback Plan

If issues arise, the system can be rolled back by:
1. Re-enabling signed URL functions in storageHelper.js
2. Re-adding URL refresh middleware to routes
3. Reverting frontend components to use signed URL functions

However, the proxy system is recommended for long-term use due to its security and simplicity benefits.
