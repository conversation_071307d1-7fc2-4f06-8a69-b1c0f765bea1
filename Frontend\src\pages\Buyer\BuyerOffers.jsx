import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Link, useNavigate } from "react-router-dom";
import { getBuyerOffers, cancelOffer } from "../../redux/slices/offerSlice";
import { setActiveTab } from "../../redux/slices/buyerDashboardSlice";
import SectionWrapper from "../../components/common/SectionWrapper";
import Table from "../../components/common/Table";
import LoadingSkeleton from "../../components/common/LoadingSkeleton";
import { ErrorDisplay } from "../../components/common/ErrorBoundary";
import Pagination from "../../components/common/Pagination";
import { FaTimes, FaCreditCard, FaCheck } from "react-icons/fa";
import { FiEye } from "react-icons/fi";
import { MdLocalOffer } from "react-icons/md";
import { toast } from "react-toastify";
import "../../styles/BuyerOffers.css";
import { getSmartFileUrl, getPlaceholderImage } from "../../utils/constants";
import { formatStandardDate } from "../../utils/dateValidation";

const BuyerOffers = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { buyerOffers, isLoading, isError, error, pagination } = useSelector(
    (state) => state.offer
  );
  const [cancellingOfferId, setCancellingOfferId] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);

  useEffect(() => {
    dispatch(getBuyerOffers({ page: currentPage, limit: 10 }));
  }, [dispatch, currentPage]);

  const handlePageChange = (newPage) => {
    setCurrentPage(newPage);
    window.scrollTo(0, 0);
  };

  const handleCancelOffer = async (offerId) => {
    if (window.confirm("Are you sure you want to cancel this offer?")) {
      setCancellingOfferId(offerId);
      try {
        await dispatch(cancelOffer(offerId)).unwrap();
        toast.success("Offer cancelled successfully");
        dispatch(getBuyerOffers({ page: currentPage, limit: 10 })); // Refresh the list
      } catch (error) {
        toast.error(error.message || "Failed to cancel offer");
      } finally {
        setCancellingOfferId(null);
      }
    }
  };

  const handleRetry = () => {
    dispatch(getBuyerOffers({ page: currentPage, limit: 10 }));
  };

  const handlePayNow = (offer) => {
    // Check if offer has an order and redirect to the existing checkout page
    if (offer.orderId && offer.orderId._id) {
      navigate(`/checkout/${offer.orderId._id}`);
    } else {
      toast.error("Order not found. Please contact support.");
    }
  };

  const handleViewOffer = (offerId) => {
    dispatch(setActiveTab("offers")); // Set active tab to offers
    navigate(`/buyer/account/offers/${offerId}`);
  };

  const getStatusBadge = (status) => {
    const statusClasses = {
      Pending: "status-pending",
      Accepted: "status-accepted",
      Rejected: "status-rejected",
      Cancelled: "status-cancelled",
      Expired: "status-expired",
    };

    return (
      <span className={`status-badge ${statusClasses[status] || ""}`}>
        {status}
      </span>
    );
  };

  const formatDate = (dateString) => {
    return formatStandardDate(dateString);
  };

  const formatPrice = (price) => {
    return `$${parseFloat(price).toFixed(2)}`;
  };

  const columns = [
    {
      label: "Content",
      key: "content",
      render: (offer) => (
        <div className="content-info">
          <div className="content-thumbnail">
            {offer.content?.thumbnailUrl ? (
              <img
                src={getSmartFileUrl(offer.content.thumbnailUrl)}
                alt={offer.content.title}
                onError={(e) => {
                  e.target.src = getPlaceholderImage(80, 80, "No Image");
                }}
              />
            ) : (
              <div className="no-thumbnail">
                <MdLocalOffer />
              </div>
            )}
          </div>
          <div className="content-details">
            <h4 className="content-title">
              {offer.content?.title || "Untitled"}
            </h4>
            <p className="content-sport">{offer.content?.sport || "N/A"}</p>
          </div>
        </div>
      ),
    },
    {
      label: "Seller",
      key: "seller",
      render: (offer) => (
        <div className="seller-info">
          <span className="seller-name">
            {offer.seller?.firstName} {offer.seller?.lastName}
          </span>
        </div>
      ),
    },
    {
      label: "Offer Amount",
      key: "amount",
      render: (offer) => (
        <span className="offer-amount">{formatPrice(offer.amount)}</span>
      ),
    },
    {
      label: "Status",
      key: "status",
      render: (offer) => getStatusBadge(offer.status),
    },
    {
      label: "Date",
      key: "createdAt",
      render: (offer) => (
        <span className="offer-date">{formatDate(offer.createdAt)}</span>
      ),
    },
    {
      label: "Actions",
      key: "actions",
      render: (offer) => (
        <div className="action-buttons">
          <button
            className="view-btn"
            onClick={() => handleViewOffer(offer._id)}
            title="View Offer Details"
          >
            <FiEye />
          </button>
          {offer.status === "Accepted" ? (
            // Check if payment is completed or expired
            offer.orderId && offer.orderId.paymentStatus === "Completed" ? (
              <button className="btn-paid" disabled>
                <FaCheck />
              </button>
            ) : offer.orderId &&
              (offer.orderId.paymentStatus === "Expired" ||
                offer.orderId.status === "Expired") ? null : (
              <button className="btn-pay" onClick={() => handlePayNow(offer)}>
                <FaCreditCard /> Pay Now
              </button>
            )
          ) : offer.status === "Pending" ? (
            <button
              className="cancel-btnmaindiv"
              onClick={() => handleCancelOffer(offer._id)}
              disabled={cancellingOfferId === offer._id}
              title="Cancel Offer"
            >
              {cancellingOfferId === offer._id ? (
                <FaSync className="spinning" />
              ) : (
                <FaTimes />
              )}
            </button>
          ) : null}
        </div>
      ),
    },
  ];

  if (isError) {
    return (
      <SectionWrapper
        icon={<MdLocalOffer className="BuyerSidebar__icon" />}
        title="My Offers"
      >
        <ErrorDisplay
          error={error}
          onRetry={handleRetry}
          title="Failed to load offers"
        />
      </SectionWrapper>
    );
  }

  return (
    <SectionWrapper
      icon={<MdLocalOffer className="BuyerSidebar__icon" />}
      title="My Offers"
    >
      <div className="BuyerOffers">
        {isLoading ? (
          <LoadingSkeleton type="table" rows={5} />
        ) : buyerOffers && buyerOffers.length > 0 ? (
          <>
            <div className="offers-summary">
              <p>
                Showing {buyerOffers.length} of {pagination.totalItems} offer
                {pagination.totalItems !== 1 ? "s" : ""}
              </p>
            </div>
            <Table
              columns={columns}
              data={buyerOffers}
              className="offers-table"
            />
            <Pagination
              currentPage={pagination.currentPage}
              totalPages={pagination.totalPages}
              onPageChange={handlePageChange}
            />
          </>
        ) : (
          <div className="no-offers">
            <MdLocalOffer className="no-offers-icon" />
            <h3>No Offers Yet</h3>
            <p>
              You haven't made any offers yet. Browse content and make your
              first offer!
            </p>
            <Link to="/content" className="btn-primary">
              Browse Content
            </Link>
          </div>
        )}
      </div>
    </SectionWrapper>
  );
};

export default BuyerOffers;
